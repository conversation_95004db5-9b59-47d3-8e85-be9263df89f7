import { auth } from '@/auth'
import AuthProvider from '@/components/auth/AuthProvider'
import { SupabaseAuthProvider } from '@/components/auth/SupabaseAuthProvider'
import ThemeProvider from '@/components/template/Theme/ThemeProvider'
import LocaleProvider from '@/components/template/LocaleProvider'
import pageMetaConfig from '@/configs/page-meta.config'
import NavigationProvider from '@/components/template/Navigation/NavigationProvider'
import { getNavigation } from '@/server/actions/navigation/getNavigation'
import { getTheme } from '@/server/actions/theme'
import { getLocale } from '@/server/actions/locale'
import type { ReactNode } from 'react'
import '@/assets/styles/app.css'

export const metadata = {
    ...pageMetaConfig,
}

export default async function RootLayout({
    children,
}: Readonly<{
    children: ReactNode
}>) {
    const session = await auth()

    const navigationTree = await getNavigation()

    const theme = await getTheme()

    const locale = await getLocale()

    // Import messages for the current locale
    const messages = (await import(`../../messages/${locale}.json`)).default

    return (
        <AuthProvider session={session}>
            <SupabaseAuthProvider>
                <html
                    className={theme.mode === 'dark' ? 'dark' : 'light'}
                    dir={theme.direction}
                    suppressHydrationWarning
                >
                    <body suppressHydrationWarning>
                        <LocaleProvider messages={messages} locale={locale}>
                            <ThemeProvider theme={theme}>
                                <NavigationProvider navigationTree={navigationTree}>
                                    {children}
                                </NavigationProvider>
                            </ThemeProvider>
                        </LocaleProvider>
                    </body>
                </html>
            </SupabaseAuthProvider>
        </AuthProvider>
    )
}
