'use client'

import { useSupabaseAuth } from '@/components/auth/SupabaseAuthProvider'
import useCurrentSession from './useCurrentSession'

export const useAuth = () => {
    const { user: supabaseUser, session: supabaseSession, loading: supabaseLoading, signOut: supabaseSignOut } = useSupabaseAuth()
    const { session: nextAuthSession } = useCurrentSession()

    // Prefer Supabase auth if available, fallback to NextAuth
    const user = supabaseUser || nextAuthSession?.user
    const isAuthenticated = !!user
    const loading = supabaseLoading

    const signOut = async () => {
        await supabaseSignOut()
        // You might also want to sign out from NextAuth if needed
        // await signOut() from next-auth
    }

    return {
        user,
        isAuthenticated,
        loading,
        signOut,
        supabaseUser,
        supabaseSession,
        nextAuthSession,
    }
}
