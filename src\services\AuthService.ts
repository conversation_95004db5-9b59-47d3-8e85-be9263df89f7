import { supabase, isSupabaseReady } from '@/lib/supabase'
import type {
    SignUpCredential,
    ForgotPassword,
    ResetPassword,
    SignUpResponse,
    SignInCredential,
} from '@/@types/auth'

export async function apiSignUp(data: SignUpCredential): Promise<SignUpResponse> {
    if (!isSupabaseReady()) {
        throw new Error('Supabase is not configured. Please check your environment variables.')
    }

    try {
        const { data: authData, error } = await supabase.auth.signUp({
            email: data.email,
            password: data.password,
            options: {
                data: {
                    user_name: data.userName,
                    full_name: data.userName,
                }
            }
        })

        if (error) {
            throw new Error(error.message)
        }

        if (!authData.user) {
            throw new Error('Failed to create user')
        }

        return {
            user: {
                id: authData.user.id,
                email: authData.user.email!,
                userName: data.userName,
            }
        }
    } catch (error) {
        throw error
    }
}

export async function apiSignIn(data: SignInCredential) {
    if (!isSupabaseReady()) {
        throw new Error('Supabase is not configured. Please check your environment variables.')
    }

    try {
        const { data: authData, error } = await supabase.auth.signInWithPassword({
            email: data.email,
            password: data.password,
        })

        if (error) {
            throw new Error(error.message)
        }

        if (!authData.user) {
            throw new Error('Invalid credentials')
        }

        return {
            user: {
                id: authData.user.id,
                email: authData.user.email!,
                userName: authData.user.user_metadata?.user_name || authData.user.email!.split('@')[0],
                avatar: authData.user.user_metadata?.avatar_url,
            }
        }
    } catch (error) {
        throw error
    }
}

export async function apiForgotPassword(data: ForgotPassword) {
    if (!isSupabaseReady()) {
        throw new Error('Supabase is not configured. Please check your environment variables.')
    }

    try {
        const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
            redirectTo: `${window.location.origin}/reset-password`,
        })

        if (error) {
            throw new Error(error.message)
        }

        return { success: true }
    } catch (error) {
        throw error
    }
}

export async function apiResetPassword(data: ResetPassword) {
    if (!isSupabaseReady()) {
        throw new Error('Supabase is not configured. Please check your environment variables.')
    }

    try {
        const { error } = await supabase.auth.updateUser({
            password: data.password
        })

        if (error) {
            throw new Error(error.message)
        }

        return { success: true }
    } catch (error) {
        throw error
    }
}

export async function apiSignOut() {
    if (!isSupabaseReady()) {
        throw new Error('Supabase is not configured. Please check your environment variables.')
    }

    try {
        const { error } = await supabase.auth.signOut()

        if (error) {
            throw new Error(error.message)
        }

        return { success: true }
    } catch (error) {
        throw error
    }
}
