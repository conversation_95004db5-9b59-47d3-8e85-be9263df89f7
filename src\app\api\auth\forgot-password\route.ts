import { NextRequest, NextResponse } from 'next/server'
import { serverApiForgotPassword } from '@/services/ServerAuthService'
import type { ForgotPassword } from '@/@types/auth'

export async function POST(request: NextRequest) {
    try {
        const body: ForgotPassword = await request.json()
        const result = await serverApiForgotPassword(body)
        return NextResponse.json(result)
    } catch (error) {
        console.error('Forgot password error:', error)
        const errorMessage = error instanceof Error ? error.message : 'An error occurred'
        return NextResponse.json({ error: errorMessage }, { status: 400 })
    }
}
