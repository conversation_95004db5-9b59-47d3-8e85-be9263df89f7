import { NextRequest, NextResponse } from 'next/server'
import { serverApiSignUp } from '@/services/ServerAuthService'
import type { SignUpCredential } from '@/@types/auth'

export async function POST(request: NextRequest) {
    try {
        const body: SignUpCredential = await request.json()
        const result = await serverApiSignUp(body)
        return NextResponse.json(result)
    } catch (error) {
        console.error('Sign up error:', error)
        const errorMessage = error instanceof Error ? error.message : 'An error occurred during sign up'
        return NextResponse.json({ error: errorMessage }, { status: 400 })
    }
}
