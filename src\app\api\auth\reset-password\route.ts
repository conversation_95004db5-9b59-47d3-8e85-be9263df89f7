import { NextRequest, NextResponse } from 'next/server'
import { apiResetPassword } from '@/services/AuthService'
import type { ResetPassword } from '@/@types/auth'

export async function POST(request: NextRequest) {
    try {
        const body: ResetPassword = await request.json()
        const result = await apiResetPassword(body)
        return NextResponse.json(result)
    } catch (error) {
        console.error('Reset password error:', error)
        const errorMessage = error instanceof Error ? error.message : 'An error occurred'
        return NextResponse.json({ error: errorMessage }, { status: 400 })
    }
}
