import { supabaseAdmin, isSupabaseReady } from '@/lib/supabase'
import type {
    SignUpCredential,
    ForgotPassword,
    SignUpResponse,
} from '@/@types/auth'

export async function serverApiSignUp(data: SignUpCredential): Promise<SignUpResponse> {
    if (!isSupabaseReady()) {
        throw new Error('Supabase is not configured. Please check your environment variables.')
    }
    
    try {
        const { data: authData, error } = await supabaseAdmin.auth.admin.createUser({
            email: data.email,
            password: data.password,
            user_metadata: {
                user_name: data.userName,
                full_name: data.userName,
            },
            email_confirm: true, // Auto-confirm email for admin-created users
        })

        if (error) {
            throw new Error(error.message)
        }

        if (!authData.user) {
            throw new Error('Failed to create user')
        }

        return {
            user: {
                id: authData.user.id,
                email: authData.user.email!,
                userName: data.userName,
            }
        }
    } catch (error) {
        throw error
    }
}

export async function serverApiForgotPassword(data: ForgotPassword) {
    if (!isSupabaseReady()) {
        throw new Error('Supabase is not configured. Please check your environment variables.')
    }
    
    try {
        const { error } = await supabaseAdmin.auth.resetPasswordForEmail(data.email, {
            redirectTo: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/reset-password`,
        })

        if (error) {
            throw new Error(error.message)
        }

        return { success: true }
    } catch (error) {
        throw error
    }
}
