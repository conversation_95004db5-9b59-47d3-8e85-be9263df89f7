'use server'
import type { SignInCredential } from '@/@types/auth'
import { supabaseAdmin, isSupabaseReady } from '@/lib/supabase'

const validateCredential = async (values: SignInCredential) => {
    console.log('validateCredential called with email:', values.email)

    // If Supabase is not configured, return null (will fall back to mock data if available)
    if (!isSupabaseReady()) {
        console.warn('Supabase not configured for authentication')
        return null
    }

    try {
        console.log('Attempting Supabase authentication...')

        // Use admin client to verify user credentials
        const { data, error } = await supabaseAdmin.auth.signInWithPassword({
            email: values.email,
            password: values.password,
        })

        if (error) {
            console.error('Supabase auth error:', error.message)
            return null
        }

        if (!data.user) {
            console.error('No user returned from Supabase')
            return null
        }

        console.log('Supabase authentication successful for user:', data.user.email)

        // Return user in the format expected by NextAuth
        return {
            id: data.user.id,
            email: data.user.email!,
            userName: data.user.user_metadata?.user_name || data.user.email!.split('@')[0],
            avatar: data.user.user_metadata?.avatar_url || null,
        }
    } catch (error) {
        console.error('Authentication error:', error)
        return null
    }
}

export default validateCredential
