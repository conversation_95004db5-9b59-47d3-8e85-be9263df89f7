'use server'
import type { SignInCredential } from '@/@types/auth'
import { apiSignIn } from '@/services/AuthService'

const validateCredential = async (values: SignInCredential) => {
    try {
        const result = await apiSignIn(values)
        return result.user
    } catch (error) {
        console.error('Authentication error:', error)
        return null
    }
}

export default validateCredential
