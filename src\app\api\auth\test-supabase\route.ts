import { NextResponse } from 'next/server'
import { isSupabaseReady } from '@/lib/supabase'

export async function GET() {
    try {
        const supabaseConfigured = isSupabaseReady()
        
        return NextResponse.json({
            supabaseConfigured,
            url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
            anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
            serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
        })
    } catch (error) {
        console.error('Supabase test error:', error)
        return NextResponse.json({ error: 'Failed to test Supabase configuration' }, { status: 500 })
    }
}
