'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import type { User, Session } from '@supabase/supabase-js'

type SupabaseAuthContextType = {
    user: User | null
    session: Session | null
    loading: boolean
    signOut: () => Promise<void>
}

const SupabaseAuthContext = createContext<SupabaseAuthContextType>({
    user: null,
    session: null,
    loading: true,
    signOut: async () => {},
})

export const useSupabaseAuth = () => {
    const context = useContext(SupabaseAuthContext)
    if (!context) {
        throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider')
    }
    return context
}

type SupabaseAuthProviderProps = {
    children: React.ReactNode
}

export const SupabaseAuthProvider = ({ children }: SupabaseAuthProviderProps) => {
    const [user, setUser] = useState<User | null>(null)
    const [session, setSession] = useState<Session | null>(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        // Get initial session
        supabase.auth.getSession().then(({ data: { session } }) => {
            setSession(session)
            setUser(session?.user ?? null)
            setLoading(false)
        })

        // Listen for auth changes
        const {
            data: { subscription },
        } = supabase.auth.onAuthStateChange((_event, session) => {
            setSession(session)
            setUser(session?.user ?? null)
            setLoading(false)
        })

        return () => subscription.unsubscribe()
    }, [])

    const signOut = async () => {
        await supabase.auth.signOut()
    }

    const value = {
        user,
        session,
        loading,
        signOut,
    }

    return (
        <SupabaseAuthContext.Provider value={value}>
            {children}
        </SupabaseAuthContext.Provider>
    )
}
